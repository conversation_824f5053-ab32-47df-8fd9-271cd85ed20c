[package]
name = "base"
version = "0.1.0"
edition = "2021"

[dependencies]
once_cell = "1.19"
serde_json = "1.0"
regex = "1.10"
ipnetwork = "0.20"
chrono = { version = "0.4", features = ["serde"] }
url = "2.5"
mysql = "25.0"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.12", features = ["json"] }
anyhow = "1.0"
thiserror = "1.0"
uuid = { version = "1.0", features = ["v4"] }
base64 = "0.22"
hex = "0.4"
rand = "0.8"
itertools = "0.12"
natural_sort = "0.0.1"

[[example]]
name = "constants_usage"
path = "examples/constants_usage.rs"
