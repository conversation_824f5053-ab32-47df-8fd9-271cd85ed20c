# Base Utils Classes - Rust Conversion

This directory contains the Rust conversion of the Python `base/utils_classes.py` file from the Eterna project.

## Overview

The original Python file contained configuration classes for various network security tools and database management. This Rust version provides equivalent functionality with the following key differences:

### Key Changes from Python to Rust

1. **Enums to Structs**: Python Enum classes have been converted to Rust structs with associated constants and methods.

2. **Static Data**: Python module-level constants are now static variables using `once_cell::sync::Lazy` for lazy initialization.

3. **Type Safety**: Rust's type system provides compile-time guarantees that weren't available in the Python version.

4. **Memory Management**: Rust's ownership system eliminates the need for garbage collection while ensuring memory safety.

## Structure

- `lib.rs` - Main module file with re-exports
- `utils_classes.rs` - Main configuration structs (converted from Python Enum classes)
- `utils_constants.rs` - Application constants and configuration values
- `country_codes.rs` - Country code mappings
- `windows_server_audit_events.rs` - Windows Server audit event definitions
- `snort_classifications.rs` - Snort IDS classification data
- `Cargo.toml` - Rust package configuration

## Configuration Structs

The following configuration structs are available:

- `MysqlConfig` - MySQL database configuration
- `GeoLocationConfig` - Geolocation data configuration
- `MaliciousConfig` - Malicious data configuration
- `DaemonConfig` - Daemon log configuration
- `DhcpConfig` - DHCP log configuration
- `DnsConfig` - DNS log configuration
- `FilterLogConfig` - Filter log configuration
- `RouterConfig` - Router log configuration
- `RouterBoardConfig` - RouterBoard log configuration
- `SnortConfig` - Snort IDS configuration
- `SquidConfig` - Squid proxy configuration
- `WindowsServerConfig` - Windows Server log configuration

## Constants and Configuration

The `utils_constants.rs` module provides various application constants:

### Binary Paths
- `BINARY_PATHS` - System binary locations (nslookup, pgrep, service, sudo)
- `BinaryPaths::new(debug_mode)` - Create new binary paths with runtime validation

### Application Constants
- `PICK_AN_OPTION` - Default option text
- `ACTION_ON_ERROR` - Unicode error handling strategy
- `MAX_KEY_LENGTH` - Maximum key length
- `MIN_LOG_SIZE` - Minimum log file size (20MB)
- `CACHE_LIFE_SPAN` - Cache duration in seconds
- `LRU_CACHE_MAXSIZE` - LRU cache maximum size

### Configuration Structs
- `LogicalOperators` - AND/OR operator configuration
- `SearchSigns` - Search pattern symbols
- `Limits` - Pagination limits
- `TopsToShow` - Top results configuration
- `Refreshes` - Auto-refresh intervals
- `LastLines` - Log tail line counts
- `RecentsToShow` - Time period options
- `Timeouts` - Network timeout durations
- `MaxTries` - Retry attempt limits

### Data Arrays
- `SEASONS_LIST` - Four seasons
- `MONTHS_LIST` - Twelve months
- `HTTP_HEADERS` - Default HTTP request headers
- `LIVE_MONITOR_DB_HEADERS` - Live monitoring headers

## Usage

```rust
use base::{MysqlConfig, GeoLocationConfig, BINARY_PATHS, LIMITS, HTTP_HEADERS};

// Get MySQL data types
let id_type = MysqlConfig::ID_DATA_TYPE;
let default_type = MysqlConfig::DEFAULT_DATA_TYPE;

// Get table name for geolocation domain data
let table_name = GeoLocationConfig::get_table_name("domain");

// Get database columns for geolocation IP data
let columns = GeoLocationConfig::db_columns_ip();

// Use application constants
let default_limit = LIMITS.default;
let cache_duration = base::CACHE_LIFE_SPAN;

// Access binary paths
if let Some(nslookup_path) = &BINARY_PATHS.nslookup {
    println!("nslookup found at: {}", nslookup_path);
}

// Use HTTP headers for requests
let user_agent = HTTP_HEADERS.get("User-Agent").unwrap();
```

## Dependencies

- `once_cell` - For lazy static initialization
- `serde_json` - For JSON value handling in classification data

## Notes

1. **Incomplete Data**: The country codes, Windows Server audit events, and Snort classifications contain only a subset of the original Python data. You'll need to add the remaining entries from the original files.

2. **Settings Integration**: The original Python code used Django settings. In Rust, you'll need to implement your own configuration system or use environment variables.

3. **Natural Sorting**: The original Python used `natsorted` for natural sorting. You may want to add a natural sorting implementation for the classification lists.

4. **Database Integration**: The original code was designed for MySQL with specific Python database libraries. You'll need to adapt the SQL generation for your Rust database library of choice.

## Migration Notes

When migrating from the Python version:

1. Replace `MYSQLConfig.CONSTANT.value` with `MysqlConfig::CONSTANT`
2. Replace `Config.get_method()` with `Config::get_method()`
3. Update import statements to use the Rust module system
4. Handle the settings/configuration differently (no Django dependency)

## Future Improvements

- Add complete data sets for all imported modules
- Implement natural sorting for classification lists
- Add database integration examples
- Add comprehensive tests
- Consider using const generics for better compile-time optimization
