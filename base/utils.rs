/*!
Core utility functions for the Eterna project.

This module provides Rust equivalents of the Python utility functions
for pagination, date manipulation, string processing, file operations,
and various data transformations.
*/

use std::collections::{HashMap, BTreeMap};
use std::fs::{File, metadata};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Seek, <PERSON>k<PERSON><PERSON>};
use std::path::Path;
use chrono::{DateTime, Utc, NaiveDate, NaiveDateTime, Duration, Datelike};
use regex::Regex;
use once_cell::sync::Lazy;
use url::{Url, percent_encoding::{utf8_percent_encode, percent_decode_str, NON_ALPHANUMERIC}};
use anyhow::{Result, anyhow};
use itertools::Itertools;
use natural_sort;

use crate::utils_constants::{LRU_CACHE_MAXSIZE, SEARCH_SIGNS, RECENTS_TO_SHOW, LIMITS, LOGICAL_OPERATORS, ON_TRUE, PICK_AN_OPTION, REFRESHES, TOPS_TO_SHOW, LAST_LINES, MONTHS_LIST, SEASONS_LIST};
use crate::utils_classes::{MysqlConfig, GeoLocationConfig, MaliciousConfig};
use crate::utils_ip::is_private;

// Regex patterns
static YMD_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"\d{4}-\d{2}-\d{2}").unwrap()
});

static HMS_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"\d{2}:\d{2}:\d{2}").unwrap()
});

/// Paginate a vector or HashMap of items.
/// 
/// # Arguments
/// 
/// * `items` - The data to paginate (Vec or HashMap)
/// * `limit` - The number of items per page
/// * `page_number` - The page number to retrieve (1-based)
/// 
/// # Returns
/// 
/// The paginated data. Returns empty collection if page number is beyond available pages.
pub fn paginate_vec<T: Clone>(items: &[T], limit: usize, page_number: usize) -> Vec<T> {
    if page_number == 0 || limit == 0 {
        return Vec::new();
    }

    let total_pages = (items.len() + limit - 1) / limit;
    
    // Return empty if requesting page beyond available pages
    if page_number > total_pages {
        return Vec::new();
    }

    let start_idx = (page_number - 1) * limit;
    let end_idx = std::cmp::min(start_idx + limit, items.len());
    
    items[start_idx..end_idx].to_vec()
}

/// Paginate a HashMap of items.
pub fn paginate_map<K: Clone + Ord, V: Clone>(
    items: &HashMap<K, V>, 
    limit: usize, 
    page_number: usize
) -> HashMap<K, V> {
    if page_number == 0 || limit == 0 {
        return HashMap::new();
    }

    let sorted_items: Vec<_> = items.iter().collect();
    let total_pages = (sorted_items.len() + limit - 1) / limit;
    
    // Return empty if requesting page beyond available pages
    if page_number > total_pages {
        return HashMap::new();
    }

    let start_idx = (page_number - 1) * limit;
    let end_idx = std::cmp::min(start_idx + limit, sorted_items.len());
    
    sorted_items[start_idx..end_idx]
        .iter()
        .map(|(k, v)| ((*k).clone(), (*v).clone()))
        .collect()
}

/// Check if all values in the HashMap are zero.
/// 
/// # Examples
/// 
/// ```
/// use std::collections::HashMap;
/// use base::utils::all_values_are_0;
/// 
/// let mut map = HashMap::new();
/// map.insert("a", 0);
/// map.insert("b", 0);
/// assert_eq!(all_values_are_0(&map), true);
/// 
/// map.insert("c", 1);
/// assert_eq!(all_values_are_0(&map), false);
/// ```
pub fn all_values_are_0<K>(dictionary: &HashMap<K, i32>) -> bool {
    dictionary.values().all(|&v| v == 0)
}

/// Trim the keys of a HashMap to a specified maximum length.
/// 
/// If a key's length exceeds the maximum length, it is truncated and appended with '...'.
/// 
/// # Examples
/// 
/// ```
/// use std::collections::HashMap;
/// use base::utils::trim_keys;
/// 
/// let mut map = HashMap::new();
/// map.insert("very_long_key_name".to_string(), 42);
/// map.insert("short".to_string(), 24);
/// 
/// let trimmed = trim_keys(&map, 10);
/// assert!(trimmed.contains_key("very_long_..."));
/// assert!(trimmed.contains_key("short"));
/// ```
pub fn trim_keys<V: Clone>(dictionary: &HashMap<String, V>, max_key_length: usize) -> HashMap<String, V> {
    dictionary
        .iter()
        .map(|(k, v)| {
            let new_key = if k.len() <= max_key_length {
                k.clone()
            } else {
                format!("{}...", &k[..max_key_length])
            };
            (new_key, v.clone())
        })
        .collect()
}

/// Move a given date by a specified number of days.
/// 
/// # Arguments
/// 
/// * `ymd` - The initial date in 'YYYY-MM-DD' format
/// * `n` - The number of days to move the date (can be positive or negative)
/// 
/// # Returns
/// 
/// The new date in 'YYYY-MM-DD' format
/// 
/// # Examples
/// 
/// ```
/// use base::utils::move_n_days;
/// 
/// assert_eq!(move_n_days("2023-01-01", 5).unwrap(), "2023-01-06");
/// assert_eq!(move_n_days("2023-01-01", -5).unwrap(), "2022-12-27");
/// assert_eq!(move_n_days("2023-12-31", 1).unwrap(), "2024-01-01");
/// ```
pub fn move_n_days(ymd: &str, n: i64) -> Result<String> {
    let date = NaiveDate::parse_from_str(ymd, "%Y-%m-%d")?;
    let new_date = date + Duration::days(n);
    Ok(new_date.format("%Y-%m-%d").to_string())
}

/// Returns the current date in ISO 8601 format (YYYY-MM-DD).
/// 
/// # Examples
/// 
/// ```
/// use base::utils::get_today_ymd;
/// 
/// let today = get_today_ymd();
/// assert!(today.len() == 10); // YYYY-MM-DD format
/// ```
pub fn get_today_ymd() -> String {
    chrono::Utc::now().format("%Y-%m-%d").to_string()
}

/// Aggregate values of a list of HashMaps by summing the values of common keys.
/// 
/// # Examples
/// 
/// ```
/// use std::collections::HashMap;
/// use base::utils::aggregate_values_of_dicts;
/// 
/// let mut dict1 = HashMap::new();
/// dict1.insert("a".to_string(), 1);
/// dict1.insert("b".to_string(), 2);
/// 
/// let mut dict2 = HashMap::new();
/// dict2.insert("a".to_string(), 3);
/// dict2.insert("c".to_string(), 4);
/// 
/// let result = aggregate_values_of_dicts(&[dict1, dict2]);
/// assert_eq!(result.get("a"), Some(&4));
/// assert_eq!(result.get("b"), Some(&2));
/// assert_eq!(result.get("c"), Some(&4));
/// ```
pub fn aggregate_values_of_dicts(list_of_dicts: &[HashMap<String, i32>]) -> HashMap<String, i32> {
    let mut aggregated = HashMap::new();
    
    for dict in list_of_dicts {
        for (key, value) in dict {
            *aggregated.entry(key.clone()).or_insert(0) += value;
        }
    }
    
    aggregated
}

/// Quote a domain name by percent-encoding special characters.
/// 
/// # Examples
/// 
/// ```
/// use base::utils::quote_domain;
/// 
/// assert_eq!(quote_domain("example.com"), "example.com");
/// assert_eq!(quote_domain("example.com/test"), "example.com%2Ftest");
/// ```
pub fn quote_domain(domain: &str) -> String {
    utf8_percent_encode(domain, NON_ALPHANUMERIC).to_string()
}

/// Decode a percent-encoded domain name.
/// 
/// # Examples
/// 
/// ```
/// use base::utils::unquote_domain;
/// 
/// assert_eq!(unquote_domain("example%2Ecom"), "example.com");
/// assert_eq!(unquote_domain("subdomain%2Eexample%2Ecom"), "subdomain.example.com");
/// ```
pub fn unquote_domain(domain: &str) -> String {
    percent_decode_str(domain)
        .decode_utf8()
        .unwrap_or_default()
        .to_string()
}

/// Generate a list of dates between the given start_date and end_date, inclusive.
/// 
/// # Examples
/// 
/// ```
/// use base::utils::create_date_range;
/// 
/// let range = create_date_range(Some("2023-01-01"), Some("2023-01-03")).unwrap();
/// assert_eq!(range, vec!["2023-01-01", "2023-01-02", "2023-01-03"]);
/// 
/// let single = create_date_range(Some("2023-01-01"), None).unwrap();
/// assert_eq!(single, vec!["2023-01-01"]);
/// ```
pub fn create_date_range(start_date: Option<&str>, end_date: Option<&str>) -> Result<Vec<String>> {
    match (start_date, end_date) {
        (None, None) => Ok(Vec::new()),
        (Some(start), None) => Ok(vec![start.to_string()]),
        (None, Some(end)) => Ok(vec![end.to_string()]),
        (Some(start), Some(end)) => {
            let start_date = NaiveDate::parse_from_str(start, "%Y-%m-%d")?;
            let end_date = NaiveDate::parse_from_str(end, "%Y-%m-%d")?;
            
            let mut dates = Vec::new();
            let mut current = start_date;
            
            while current <= end_date {
                dates.push(current.format("%Y-%m-%d").to_string());
                current += Duration::days(1);
            }
            
            Ok(dates)
        }
    }
}

/// Return the middle item of a vector.
/// 
/// # Examples
/// 
/// ```
/// use base::utils::pick_middle_item_of_list;
/// 
/// assert_eq!(pick_middle_item_of_list(&[1, 2, 3, 4, 5]), Some(&3));
/// assert_eq!(pick_middle_item_of_list(&[1, 2, 3, 4]), Some(&2));
/// assert_eq!(pick_middle_item_of_list::<i32>(&[]), None);
/// ```
pub fn pick_middle_item_of_list<T>(items: &[T]) -> Option<&T> {
    if items.is_empty() {
        None
    } else {
        Some(&items[(items.len() - 1) / 2])
    }
}

/// Strip the protocol (http or https) and path from a given URL, leaving only the domain.
/// 
/// # Examples
/// 
/// ```
/// use base::utils::strip_protocol_and_path_from_url;
/// 
/// assert_eq!(strip_protocol_and_path_from_url("https://example.com/path/to/resource?query=param"), "example.com");
/// assert_eq!(strip_protocol_and_path_from_url("http://example.com"), "example.com");
/// assert_eq!(strip_protocol_and_path_from_url("https://example.com/"), "example.com");
/// ```
pub fn strip_protocol_and_path_from_url(url: &str) -> String {
    let mut url = url.to_string();
    
    if url.starts_with("https://") {
        url = url.strip_prefix("https://").unwrap_or(&url).to_string();
    } else if url.starts_with("http://") {
        url = url.strip_prefix("http://").unwrap_or(&url).to_string();
    }
    
    // Remove query parameters
    if let Some(pos) = url.find('?') {
        url = url[..pos].to_string();
    }
    
    // Remove path
    if let Some(pos) = url.find('/') {
        url = url[..pos].to_string();
    }
    
    url.trim_end_matches('/').to_string()
}

/// Normalize a DNS question name by replacing all occurrences of numbers
/// enclosed in parentheses with dots and removing any leading or trailing dots.
/// 
/// # Examples
/// 
/// ```
/// use base::utils::normalize_dns_question_name;
/// 
/// assert_eq!(normalize_dns_question_name("example(123)com"), "example.com");
/// assert_eq!(normalize_dns_question_name("(456)example(789)com"), "example.com");
/// ```
pub fn normalize_dns_question_name(url: &str) -> String {
    static PATTERN: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"\([0-9]+\)").unwrap()
    });
    
    PATTERN.replace_all(url, ".").trim_matches('.').to_string()
}

/// Normalize a date string to the format 'YYYY-MM-DD'.
/// 
/// This function attempts to parse the input date string using the formats
/// '%m/%d/%Y' and '%m/%d/%y'. If parsing is successful, it returns the date
/// in the 'YYYY-MM-DD' format. If parsing fails for all formats, it returns
/// the original date string.
/// 
/// # Examples
/// 
/// ```
/// use base::utils::normalize_date;
/// 
/// assert_eq!(normalize_date("12/31/2020"), "2020-12-31");
/// assert_eq!(normalize_date("12/31/20"), "2020-12-31");
/// assert_eq!(normalize_date("31/12/2020"), "31/12/2020"); // Invalid format, returns original
/// ```
pub fn normalize_date(date: &str) -> String {
    let formats = ["%m/%d/%Y", "%m/%d/%y"];
    
    for format in &formats {
        if let Ok(parsed_date) = NaiveDate::parse_from_str(date, format) {
            return parsed_date.format("%Y-%m-%d").to_string();
        }
    }
    
    // If all formats fail, return the original date string
    date.to_string()
}

/// Normalize a given time string to the 24-hour format 'HH:MM:SS'.
/// 
/// # Examples
/// 
/// ```
/// use base::utils::normalize_time;
/// 
/// assert_eq!(normalize_time("12:00:00 AM"), "00:00:00");
/// assert_eq!(normalize_time("12:00:00 PM"), "12:00:00");
/// assert_eq!(normalize_time("10:12:34 PM"), "22:12:34");
/// assert_eq!(normalize_time("invalid time"), "invalid time");
/// ```
pub fn normalize_time(time: &str) -> String {
    let formats = ["%I:%M:%S %p", "%H:%M:%S %p"];
    
    for format in &formats {
        if let Ok(parsed_time) = NaiveDateTime::parse_from_str(&format!("1970-01-01 {}", time), &format!("1970-01-01 {}", format)) {
            return parsed_time.format("%H:%M:%S").to_string();
        }
    }
    
    // If all formats fail, return the original time string
    time.to_string()
}

/// Normalize a Windows Server category string by converting it to lowercase
/// and removing non-alphanumeric characters.
///
/// # Examples
///
/// ```
/// use base::utils::normalize_windowsserver_category;
///
/// assert_eq!(normalize_windowsserver_category("Logon/Logoff"), "logonlogoff");
/// assert_eq!(normalize_windowsserver_category("Account Management"), "accountmanagement");
/// assert_eq!(normalize_windowsserver_category("ds-access"), "dsaccess");
/// ```
pub fn normalize_windowsserver_category(category: &str) -> String {
    static PATTERN: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"[^0-9a-zA-Z]+").unwrap()
    });

    PATTERN.replace_all(&category.to_lowercase(), "").to_string()
}

/// Get a list of parsed directories in a given directory, sorted by natural order.
///
/// # Examples
///
/// ```
/// use base::utils::get_parsed_dirs;
///
/// let dirs = get_parsed_dirs("/path/to/directory", false);
/// // Returns directories matching YYYY-MM-DD format, sorted naturally
/// ```
pub fn get_parsed_dirs(directory: &str, reverse: bool) -> Vec<String> {
    let path = Path::new(directory);
    if !path.exists() {
        return Vec::new();
    }

    let mut dirs = Vec::new();
    if let Ok(entries) = std::fs::read_dir(path) {
        for entry in entries.flatten() {
            if let Ok(file_type) = entry.file_type() {
                if file_type.is_dir() {
                    if let Some(name) = entry.file_name().to_str() {
                        if is_ymd(name) {
                            dirs.push(name.to_string());
                        }
                    }
                }
            }
        }
    }

    dirs.sort_by(|a, b| natural_sort::compare(a, b));
    if reverse {
        dirs.reverse();
    }

    dirs
}

/// Check if a string matches the YYYY-MM-DD date format.
///
/// # Examples
///
/// ```
/// use base::utils::is_ymd;
///
/// assert!(is_ymd("2023-12-31"));
/// assert!(is_ymd("2000-01-01"));
/// assert!(!is_ymd("23-12-31"));
/// assert!(!is_ymd("2023-13-01"));
/// ```
pub fn is_ymd(string: &str) -> bool {
    static PATTERN: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"^\d{4}-\d{2}-\d{2}$").unwrap()
    });

    if !PATTERN.is_match(string) {
        return false;
    }

    // Additional validation using chrono
    NaiveDate::parse_from_str(string, "%Y-%m-%d").is_ok()
}

/// Convert a list of tuples into a flat list.
///
/// # Examples
///
/// ```
/// use base::utils::list_of_tuples_to_list;
///
/// let tuples = vec![(1,), (2,), (3,)];
/// let result = list_of_tuples_to_list(&tuples);
/// assert_eq!(result, vec![1, 2, 3]);
/// ```
pub fn list_of_tuples_to_list<T: Clone>(lst: &[(T,)]) -> Vec<T> {
    lst.iter().map(|(item,)| item.clone()).collect()
}

/// Extract the date from the log file name.
///
/// The log file name is expected to be in the format 'YYYY-MM-DD--Day.log'.
/// This function removes the '--Day.log' part and returns only the 'YYYY-MM-DD' part.
///
/// # Examples
///
/// ```
/// use base::utils::get_date_of_source_log;
///
/// assert_eq!(get_date_of_source_log("/FOO/BAR/BAZ/2023-05-12--Fri.log"), "2023-05-12");
/// assert_eq!(get_date_of_source_log("/var/logs/2022-11-23--Wed.log"), "2022-11-23");
/// ```
pub fn get_date_of_source_log(log_path: &str) -> String {
    static PATTERN: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"--.*$").unwrap()
    });

    let path = Path::new(log_path);
    if let Some(filename) = path.file_name().and_then(|f| f.to_str()) {
        PATTERN.replace(filename, "").to_string()
    } else {
        String::new()
    }
}

/// Get the size of the source log file.
///
/// # Examples
///
/// ```
/// use base::utils::get_size_of_source_log;
///
/// let size = get_size_of_source_log("/path/to/existing/logfile.log");
/// // Returns file size in bytes, or 0 if file doesn't exist
/// ```
pub fn get_size_of_source_log(log_path: &str) -> u64 {
    metadata(log_path)
        .map(|m| m.len())
        .unwrap_or(0)
}

/// Convert a date string from 'YYYY-MM-DD' or 'YYYY_MM_DD' format to 'YYYY-MM' or 'YYYY_MM' format.
///
/// # Examples
///
/// ```
/// use base::utils::ymd_to_ym;
///
/// assert_eq!(ymd_to_ym("2023-10-05"), "2023-10");
/// assert_eq!(ymd_to_ym("2023_10_05"), "2023_10");
/// assert_eq!(ymd_to_ym("1999-12"), "1999-12");
/// assert_eq!(ymd_to_ym("********"), "********"); // No change for invalid format
/// ```
pub fn ymd_to_ym(ymd: &str) -> String {
    if ymd.contains('-') {
        static DASH_PATTERN: Lazy<Regex> = Lazy::new(|| {
            Regex::new(r"^(\d{4}-\d{2})-\d{2}").unwrap()
        });

        if let Some(captures) = DASH_PATTERN.captures(ymd) {
            return captures[1].to_string();
        }
    }

    if ymd.contains('_') {
        static UNDERSCORE_PATTERN: Lazy<Regex> = Lazy::new(|| {
            Regex::new(r"^(\d{4}_\d{2})_\d{2}").unwrap()
        });

        if let Some(captures) = UNDERSCORE_PATTERN.captures(ymd) {
            return captures[1].to_string();
        }
    }

    ymd.to_string()
}

/// Convert all dashes ('-') in the given string to underscores ('_').
///
/// # Examples
///
/// ```
/// use base::utils::dash_to_underscore;
///
/// assert_eq!(dash_to_underscore("Sensor-One"), "Sensor_One");
/// assert_eq!(dash_to_underscore("2024-11-30"), "2024_11_30");
/// ```
pub fn dash_to_underscore(string: &str) -> String {
    string.replace('-', "_")
}

/// Convert all underscores ('_') in the given string to dashes ('-').
///
/// # Examples
///
/// ```
/// use base::utils::underscore_to_dash;
///
/// assert_eq!(underscore_to_dash("Sensor_One"), "Sensor-One");
/// assert_eq!(underscore_to_dash("2024_11_30"), "2024-11-30");
/// ```
pub fn underscore_to_dash(string: &str) -> String {
    string.replace('_', "-")
}

/// Break down the name of a database into its components.
///
/// # Examples
///
/// ```
/// use base::utils::break_name_of_database;
///
/// let (slug, object_name, ymd) = break_name_of_database("daemon__Sensor_One__2024_11_30");
/// assert_eq!(slug, "daemon");
/// assert_eq!(object_name, "Sensor_One");
/// assert_eq!(ymd, "2024_11_30");
///
/// let (slug, object_name, ymd) = break_name_of_database("dhcp__2024_11_30");
/// assert_eq!(slug, "dhcp");
/// assert_eq!(object_name, "");
/// assert_eq!(ymd, "2024_11_30");
/// ```
pub fn break_name_of_database(database_name: &str) -> (String, String, String) {
    // Check if it's a non-dated database
    let non_dated_databases = ["geolocation", "malicious"]; // From MysqlConfig::NON_DATED_DATABASES
    if non_dated_databases.contains(&database_name) {
        return (database_name.to_string(), String::new(), String::new());
    }

    let parts: Vec<&str> = database_name.split("__").collect();

    match parts.len() {
        2 => (parts[0].to_string(), String::new(), parts[1].to_string()),
        3 => (parts[0].to_string(), parts[1].to_string(), parts[2].to_string()),
        _ => (String::new(), String::new(), String::new()),
    }
}

/// Generate the name of a database based on the provided slug, date, and object name.
///
/// # Examples
///
/// ```
/// use base::utils::create_name_of_database;
///
/// assert_eq!(create_name_of_database("daemon", "2024-11-30", "Sensor-One"), "daemon__Sensor_One__2024_11_30");
/// assert_eq!(create_name_of_database("dhcp", "2024-11-30", ""), "dhcp__2024_11_30");
/// assert_eq!(create_name_of_database("malicious", "", ""), "malicious");
/// ```
pub fn create_name_of_database(slug: &str, ymd: &str, object_name: &str) -> String {
    let non_dated_databases = ["geolocation", "malicious"]; // From MysqlConfig::NON_DATED_DATABASES
    if non_dated_databases.contains(&slug) {
        return slug.to_string();
    }

    let ymd_underscore = dash_to_underscore(ymd);

    if object_name.is_empty() {
        format!("{}__{}", slug, ymd_underscore)
    } else {
        let object_name_underscore = dash_to_underscore(object_name);
        format!("{}__{}__{}", slug, object_name_underscore, ymd_underscore)
    }
}

/// Get recent, date to show, and date end to show values.
///
/// This function processes the input parameters and returns appropriate values
/// for recent, date_to_show, and date_end_to_show based on the logic from the original Python code.
///
/// # Arguments
///
/// * `recent` - Optional recent parameter
/// * `date` - Optional date parameter
/// * `date_end` - Optional date end parameter
///
/// # Returns
///
/// A tuple of (recent_to_show, date_to_show, date_end_to_show)
pub fn get_rts_dts_dets(
    recent: Option<&str>,
    date: Option<&str>,
    date_end: Option<&str>,
) -> (Option<String>, String, Option<String>) {
    let today = get_today_ymd();

    let recent_to_show = recent.map(|r| r.to_string());

    let date_to_show = if let Some(recent_val) = recent {
        match recent_val {
            "yesterday" => move_n_days(&today, -1).unwrap_or(today.clone()),
            "last_week" => move_n_days(&today, -7).unwrap_or(today.clone()),
            "last_month" => move_n_days(&today, -30).unwrap_or(today.clone()),
            _ => date.unwrap_or(&today).to_string(),
        }
    } else {
        date.unwrap_or(&today).to_string()
    };

    let date_end_to_show = if recent.is_some() {
        Some(today)
    } else {
        date_end.map(|d| d.to_string())
    };

    (recent_to_show, date_to_show, date_end_to_show)
}

/// Read statistics from a file (placeholder implementation).
///
/// This function would read statistics from a file in the original Python implementation.
/// For now, this is a placeholder that returns an empty HashMap.
///
/// # Arguments
///
/// * `file_path` - Path to the statistics file
///
/// # Returns
///
/// A HashMap containing the statistics data
pub fn read_statistics_file(file_path: &str) -> HashMap<String, i32> {
    // Placeholder implementation
    // In a real implementation, this would read and parse a statistics file
    HashMap::new()
}

/// Get the last N lines from a file.
///
/// # Arguments
///
/// * `file_path` - Path to the file
/// * `n` - Number of lines to read from the end
///
/// # Returns
///
/// A vector of strings representing the last N lines
pub fn get_last_n_lines(file_path: &str, n: usize) -> Result<Vec<String>> {
    use std::io::{BufRead, BufReader, Seek, SeekFrom};
    use std::fs::File;

    let file = File::open(file_path)?;
    let mut reader = BufReader::new(file);

    // Read all lines into memory (for simplicity)
    // For very large files, a more efficient approach would be needed
    let lines: Result<Vec<String>, _> = reader.lines().collect();
    let lines = lines?;

    if lines.len() <= n {
        Ok(lines)
    } else {
        Ok(lines[lines.len() - n..].to_vec())
    }
}

/// Convert a file size in bytes to a human-readable string.
///
/// # Arguments
///
/// * `bytes` - The file size in bytes
///
/// # Returns
///
/// A human-readable string representation of the file size
///
/// # Examples
///
/// ```
/// use base::utils::humanize_bytes;
///
/// assert_eq!(humanize_bytes(1024), "1.0 KB");
/// assert_eq!(humanize_bytes(1048576), "1.0 MB");
/// assert_eq!(humanize_bytes(1073741824), "1.0 GB");
/// ```
pub fn humanize_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB", "PB"];
    const THRESHOLD: f64 = 1024.0;

    if bytes == 0 {
        return "0 B".to_string();
    }

    let bytes_f = bytes as f64;
    let unit_index = (bytes_f.log10() / THRESHOLD.log10()).floor() as usize;
    let unit_index = unit_index.min(UNITS.len() - 1);

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[0])
    } else {
        let size = bytes_f / THRESHOLD.powi(unit_index as i32);
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_paginate_vec() {
        let items = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

        let page1 = paginate_vec(&items, 3, 1);
        assert_eq!(page1, vec![1, 2, 3]);

        let page2 = paginate_vec(&items, 3, 2);
        assert_eq!(page2, vec![4, 5, 6]);

        let page_beyond = paginate_vec(&items, 3, 10);
        assert!(page_beyond.is_empty());
    }

    #[test]
    fn test_all_values_are_0() {
        let mut map = HashMap::new();
        map.insert("a", 0);
        map.insert("b", 0);
        assert!(all_values_are_0(&map));

        map.insert("c", 1);
        assert!(!all_values_are_0(&map));
    }

    #[test]
    fn test_move_n_days() {
        assert_eq!(move_n_days("2023-01-01", 5).unwrap(), "2023-01-06");
        assert_eq!(move_n_days("2023-01-01", -5).unwrap(), "2022-12-27");
        assert_eq!(move_n_days("2023-12-31", 1).unwrap(), "2024-01-01");
    }

    #[test]
    fn test_strip_protocol_and_path_from_url() {
        assert_eq!(strip_protocol_and_path_from_url("https://example.com/path/to/resource?query=param"), "example.com");
        assert_eq!(strip_protocol_and_path_from_url("http://example.com"), "example.com");
        assert_eq!(strip_protocol_and_path_from_url("https://example.com/"), "example.com");
        assert_eq!(strip_protocol_and_path_from_url("http://example.com?query=param"), "example.com");
    }

    #[test]
    fn test_normalize_dns_question_name() {
        assert_eq!(normalize_dns_question_name("example(123)com"), "example.com");
        assert_eq!(normalize_dns_question_name("(456)example(789)com"), "example.com");
        assert_eq!(normalize_dns_question_name("example(123)com(456)"), "example.com");
    }

    #[test]
    fn test_normalize_date() {
        assert_eq!(normalize_date("12/31/2020"), "2020-12-31");
        assert_eq!(normalize_date("12/31/20"), "2020-12-31");
        assert_eq!(normalize_date("31/12/2020"), "31/12/2020"); // Invalid format
    }

    #[test]
    fn test_normalize_windowsserver_category() {
        assert_eq!(normalize_windowsserver_category("Logon/Logoff"), "logonlogoff");
        assert_eq!(normalize_windowsserver_category("Account Management"), "accountmanagement");
        assert_eq!(normalize_windowsserver_category("ds-access"), "dsaccess");
    }

    #[test]
    fn test_is_ymd() {
        assert!(is_ymd("2023-12-31"));
        assert!(is_ymd("2000-01-01"));
        assert!(!is_ymd("23-12-31"));
        assert!(!is_ymd("2023-13-01"));
        assert!(!is_ymd("2023-12-32"));
    }

    #[test]
    fn test_ymd_to_ym() {
        assert_eq!(ymd_to_ym("2023-10-05"), "2023-10");
        assert_eq!(ymd_to_ym("2023_10_05"), "2023_10");
        assert_eq!(ymd_to_ym("1999-12"), "1999-12");
        assert_eq!(ymd_to_ym("********"), "********");
    }

    #[test]
    fn test_break_name_of_database() {
        let (slug, object_name, ymd) = break_name_of_database("daemon__Sensor_One__2024_11_30");
        assert_eq!(slug, "daemon");
        assert_eq!(object_name, "Sensor_One");
        assert_eq!(ymd, "2024_11_30");

        let (slug, object_name, ymd) = break_name_of_database("dhcp__2024_11_30");
        assert_eq!(slug, "dhcp");
        assert_eq!(object_name, "");
        assert_eq!(ymd, "2024_11_30");

        let (slug, object_name, ymd) = break_name_of_database("geolocation");
        assert_eq!(slug, "geolocation");
        assert_eq!(object_name, "");
        assert_eq!(ymd, "");
    }

    #[test]
    fn test_create_name_of_database() {
        assert_eq!(create_name_of_database("daemon", "2024-11-30", "Sensor-One"), "daemon__Sensor_One__2024_11_30");
        assert_eq!(create_name_of_database("dhcp", "2024-11-30", ""), "dhcp__2024_11_30");
        assert_eq!(create_name_of_database("malicious", "", ""), "malicious");
    }
}
