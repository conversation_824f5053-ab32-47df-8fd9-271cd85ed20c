/*!
Example usage of the utils_constants module.

This example demonstrates how to use the various constants and configuration
structs provided by the utils_constants module.
*/

use base::{
    BINARY_PATHS, LIMITS, TIMEOUTS, HTTP_HEADERS, SEASONS_LIST, MONTHS_LIST,
    BinaryPaths, LogicalOperators, LOGICAL_OPERATORS, SEARCH_SIGNS,
    CACHE_LIFE_SPAN, LRU_CACHE_MAXSIZE, MIN_LOG_SIZE,
};

fn main() {
    println!("=== Base Utils Constants Example ===\n");

    // Application constants
    println!("Application Constants:");
    println!("  Cache lifespan: {} seconds", CACHE_LIFE_SPAN);
    println!("  LRU cache max size: {}", LRU_CACHE_MAXSIZE);
    println!("  Minimum log size: {} bytes ({} MB)", MIN_LOG_SIZE, MIN_LOG_SIZE / (1024 * 1024));
    println!();

    // Binary paths
    println!("Binary Paths:");
    println!("  nslookup: {:?}", BINARY_PATHS.nslookup);
    println!("  pgrep: {:?}", BINARY_PATHS.pgrep);
    println!("  service: {:?}", BINARY_PATHS.service);
    println!("  sudo: {:?}", BINARY_PATHS.sudo);
    println!();

    // Create binary paths with debug mode
    match BinaryPaths::new(true) {
        Ok(debug_paths) => {
            println!("Debug mode binary paths created successfully");
            println!("  nslookup: {:?}", debug_paths.nslookup);
        }
        Err(e) => println!("Error creating binary paths: {}", e),
    }
    println!();

    // Configuration structs
    println!("Configuration:");
    println!("  Default limit: {}", LIMITS.default);
    println!("  Available limits: {:?}", LIMITS.values);
    println!("  Logical operators: {:?}", LOGICAL_OPERATORS.values);
    println!("  Default logical operator: {}", LOGICAL_OPERATORS.default);
    println!();

    // Search signs
    println!("Search Signs:");
    println!("  Asterisk: '{}'", SEARCH_SIGNS.asterisk);
    println!("  Caret: '{}'", SEARCH_SIGNS.caret);
    println!("  Dollar: '{}'", SEARCH_SIGNS.dollar);
    println!("  Field separator: '{}'", SEARCH_SIGNS.field_separator);
    println!();

    // Timeouts
    println!("Timeouts:");
    println!("  Tor timeout: {:?}", TIMEOUTS.is_tor);
    println!("  Fetcher timeout: {:?}", TIMEOUTS.fetcher);
    println!();

    // Data arrays
    println!("Seasons: {:?}", SEASONS_LIST);
    println!("Months: {:?}", MONTHS_LIST);
    println!();

    // HTTP headers
    println!("HTTP Headers:");
    for (key, value) in HTTP_HEADERS.iter() {
        println!("  {}: {}", key, value);
    }
    println!();

    println!("=== Example completed ===");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_constants_values() {
        assert_eq!(CACHE_LIFE_SPAN, 1800);
        assert_eq!(LRU_CACHE_MAXSIZE, 500);
        assert_eq!(MIN_LOG_SIZE, 20 * 1024 * 1024);
        assert_eq!(LIMITS.default, 25);
        assert_eq!(LOGICAL_OPERATORS.default, "AND");
        assert_eq!(SEASONS_LIST.len(), 4);
        assert_eq!(MONTHS_LIST.len(), 12);
    }

    #[test]
    fn test_binary_paths_creation() {
        // Test that we can create binary paths in debug mode
        let result = BinaryPaths::new(true);
        assert!(result.is_ok());
    }

    #[test]
    fn test_http_headers() {
        assert!(HTTP_HEADERS.contains_key("User-Agent"));
        assert!(HTTP_HEADERS.contains_key("Accept"));
        assert!(HTTP_HEADERS.contains_key("DNT"));
    }

    #[test]
    fn test_search_signs() {
        assert_eq!(SEARCH_SIGNS.asterisk, "*");
        assert_eq!(SEARCH_SIGNS.caret, "^");
        assert_eq!(SEARCH_SIGNS.dollar, "$");
        assert_eq!(SEARCH_SIGNS.field_separator, "||");
    }
}
