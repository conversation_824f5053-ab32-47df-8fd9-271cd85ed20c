/*!
Base module containing utility classes and configuration for the Eterna project.

This module provides Rust equivalents of the Python utility classes used for
database configuration, log parsing, and data management across various
network security tools and services.
*/

pub mod utils_classes;
pub mod utils_constants;
pub mod country_codes;
pub mod windows_server_audit_events;
pub mod snort_classifications;

// Re-export the main configuration structs for easier access
pub use utils_classes::{
    MysqlConfig, GeoLocationConfig, MaliciousConfig, DaemonConfig, DhcpConfig,
    DnsConfig, FilterLogConfig, RouterConfig, RouterBoardConfig, SnortConfig,
    SquidConfig, WindowsServerConfig,
};

// Re-export constants
pub use utils_classes::{HOUR_KEYS, EVENT_TYPES, EVENT_TYPES_CRITICALS, EVENT_TYPES_WARNINGS};
pub use utils_constants::{
    PICK_AN_OPTION, ACTION_ON_ERROR, MAX_KEY_LENGTH, MIN_LOG_SIZE, PRINT_ENDPOINT,
    CACHE_LIFE_SPAN, LRU_CACHE_MAXSIZE, BINARY_PATHS, ON_TRUE, LOGICAL_OPERATORS,
    SEARCH_SIGNS, LIMITS, TOPS_TO_SHOW, REFRESHES, LAST_LINES, RECENTS_TO_SHOW,
    TIMEOUTS, MAX_TRIES, SEASONS_LIST, MONTHS_LIST, HTTP_HEADERS, LIVE_MONITOR_DB_HEADERS,
    BinaryPaths, LogicalOperators, SearchSigns, Limits as LimitsStruct, TopsToShow,
    Refreshes as RefreshesStruct, LastLines as LastLinesStruct, RecentsToShow,
    Timeouts, MaxTries,
};

// Re-export data
pub use country_codes::COUNTRY_CODES_DICT;
pub use windows_server_audit_events::{WINDOWS_SERVER_AUDIT_EVENTS, get_category_from_event_id};
pub use snort_classifications::{
    CLASSIFICATIONS_DICT, CLASSIFICATIONS_CRITICALS, CLASSIFICATIONS_WARNINGS,
    CLASSIFICATIONS_LOWS, CLASSIFICATIONS_VERY_LOWS,
};
