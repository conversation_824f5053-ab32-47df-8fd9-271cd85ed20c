/*!
Utility constants for the Eterna project.

This module contains various configuration constants, binary paths, and data structures
used throughout the application. Converted from the Python utils_constants.py file.
*/

use std::collections::HashMap;
use std::env;
use std::path::Path;
use std::time::Duration;
use once_cell::sync::Lazy;

pub const PICK_AN_OPTION: &str = "Pick an Option";

/// To avoid Unicode decode errors
/// Note: Using encoding='ISO-8859-1' will mess up Persian/Arabic characters
pub const ACTION_ON_ERROR: &str = "ignore";

pub const MAX_KEY_LENGTH: usize = 20;

/// 20MB minimum log size
pub const MIN_LOG_SIZE: usize = 1024 * 1024 * 20;

pub const PRINT_ENDPOINT: &str = "\r";

/// Cache lifespan in seconds (30 minutes = 1800 seconds)
pub const CACHE_LIFE_SPAN: u64 = 1800;

/// LRU cache max size (default is 128)
pub const LRU_CACHE_MAXSIZE: usize = 500;

/// Binary paths configuration
#[derive(Debug, <PERSON>lone)]
pub struct BinaryPaths {
    pub nslookup: Option<String>,
    pub pgrep: Option<String>,
    pub service: Option<String>,
    pub sudo: Option<String>,
}

impl BinaryPaths {
    /// Create a new BinaryPaths instance by searching for binaries in PATH
    pub fn new(debug_mode: bool) -> Result<Self, std::io::Error> {
        let shell_path = env::var("PATH").unwrap_or_default();
        let usr_local_bin = "/usr/local/bin";
        
        // Add /usr/local/bin to PATH if not in debug mode and not already present
        let search_path = if !debug_mode && !shell_path.split(':').any(|p| p == usr_local_bin) {
            format!("{}:{}", shell_path, usr_local_bin)
        } else {
            shell_path
        };

        let binary_paths = Self {
            nslookup: which_binary("nslookup", &search_path),
            pgrep: which_binary("pgrep", &search_path),
            service: which_binary("service", &search_path),
            sudo: which_binary("sudo", &search_path),
        };

        // Pre-check for missing binaries as a runtime safeguard
        if binary_paths.service.is_none() && !debug_mode {
            return Err(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "Required binary 'service' not found in system PATH.",
            ));
        }

        Ok(binary_paths)
    }
}

/// Find a binary in the given PATH
fn which_binary(name: &str, path: &str) -> Option<String> {
    for dir in path.split(':') {
        if dir.is_empty() {
            continue;
        }
        let full_path = format!("{}/{}", dir, name);
        if Path::new(&full_path).is_file() {
            return Some(full_path);
        }
    }
    None
}

/// Global binary paths instance (lazy-initialized)
pub static BINARY_PATHS: Lazy<BinaryPaths> = Lazy::new(|| {
    // In a real application, you'd get debug_mode from your configuration
    // For now, we'll assume production mode (false)
    BinaryPaths::new(false).unwrap_or_else(|_| BinaryPaths {
        nslookup: None,
        pgrep: None,
        service: None,
        sudo: None,
    })
});

pub const ON_TRUE: [&str; 2] = ["on", "true"];

/// Logical operators configuration
#[derive(Debug, Clone)]
pub struct LogicalOperators {
    pub values: Vec<&'static str>,
    pub default: &'static str,
    pub for_field: &'static str,
}

pub const LOGICAL_OPERATORS: LogicalOperators = LogicalOperators {
    values: vec!["AND", "OR"],
    default: "AND",
    for_field: "OR",
};

/// Search signs configuration
#[derive(Debug, Clone)]
pub struct SearchSigns {
    pub asterisk: &'static str,
    pub caret: &'static str,
    pub dollar: &'static str,
    pub field_separator: &'static str,
}

pub const SEARCH_SIGNS: SearchSigns = SearchSigns {
    asterisk: "*",
    caret: "^",
    dollar: "$",
    field_separator: "||",
};

/// Limits configuration
#[derive(Debug, Clone)]
pub struct Limits {
    pub values: Vec<usize>,
    pub default: usize,
}

pub const LIMITS: Limits = Limits {
    values: vec![25, 50, 100, 200, 500],
    default: 25,
};

/// Tops to show configuration
#[derive(Debug, Clone)]
pub struct TopsToShow {
    pub values: Vec<usize>,
    pub default: usize,
}

pub const TOPS_TO_SHOW: TopsToShow = TopsToShow {
    values: vec![5, 10, 15, 20],
    default: 10,
};

/// Refresh intervals configuration
#[derive(Debug, Clone)]
pub struct Refreshes {
    pub values: Vec<usize>,
    pub default: usize,
    pub min_nonzero: usize,
}

pub const REFRESHES: Refreshes = Refreshes {
    values: vec![0, 5, 10, 30, 45, 60],
    default: 0,
    min_nonzero: 5,
};

/// Last lines configuration
#[derive(Debug, Clone)]
pub struct LastLines {
    pub values: Vec<usize>,
    pub default: usize,
    pub max: usize,
}

pub const LAST_LINES: LastLines = LastLines {
    values: vec![10, 20, 50, 100, 200],
    default: 20,
    max: 200,
};

/// Recents to show configuration
#[derive(Debug, Clone)]
pub struct RecentsToShow {
    pub values: Vec<&'static str>,
    pub default: &'static str,
}

pub const RECENTS_TO_SHOW: RecentsToShow = RecentsToShow {
    values: vec![
        "Week", "2 Weeks", "3 Weeks", "Month", "2 Months", 
        "3 Months", "6 Months", "9 Months", "Year"
    ],
    default: "2 Weeks",
};

/// Timeout configurations
#[derive(Debug, Clone)]
pub struct Timeouts {
    pub is_tor: Duration,
    /// Setting to 2 caused constant ConnectTimeout errors.
    /// 5 seems to be a proper value.
    pub fetcher: Duration,
}

pub const TIMEOUTS: Timeouts = Timeouts {
    is_tor: Duration::from_secs(5),
    fetcher: Duration::from_secs(5),
};

/// Maximum tries configuration
#[derive(Debug, Clone)]
pub struct MaxTries {
    /// High number because we are using async
    pub dl: usize,
    pub restart: usize,
    pub is_tor: usize,
}

pub const MAX_TRIES: MaxTries = MaxTries {
    dl: 10,
    restart: 10,
    is_tor: 5,
};

/// Seasons list
pub const SEASONS_LIST: [&str; 4] = [
    "Spring",
    "Summer", 
    "Fall",
    "Winter",
];

/// Months list
pub const MONTHS_LIST: [&str; 12] = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
];

/// HTTP headers for web requests
pub static HTTP_HEADERS: Lazy<HashMap<&'static str, &'static str>> = Lazy::new(|| {
    let mut headers = HashMap::new();
    
    headers.insert("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
    headers.insert("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8");
    headers.insert("Accept-Language", "en-US,en;q=0.9");
    headers.insert("Accept-Encoding", "gzip, deflate, br");
    
    // Do Not Track
    headers.insert("DNT", "1");
    
    // Using 'close' can slow down scraping or mark you as unusual
    headers.insert("Connection", "keep-alive");
    
    // This is sent by browsers when accessing HTTPS URLs
    // and signals the browser prefers secure content
    headers.insert("Upgrade-Insecure-Requests", "1");
    
    // Uncomment these if needed:
    // headers.insert("Referer", "https://example.com/");
    
    // Modern browsers send these in real requests.
    // They're important for bypassing bot protections like Cloudflare, Akamai, etc.
    // headers.insert("Sec-Fetch-Dest", "document");
    // headers.insert("Sec-Fetch-Mode", "navigate");
    // headers.insert("Sec-Fetch-Site", "none");  // or 'same-origin', 'cross-site' depending on context
    // headers.insert("Sec-Fetch-User", "?1");
    
    headers
});

/// Live monitor database headers
pub const LIVE_MONITOR_DB_HEADERS: [&str; 2] = [
    "",
    "Message",
];
